{"name": "digibox-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "env-cmd -f .env.local next dev -p 4000 --turbopack", "build:dev": "env-cmd -f .env.dev next build", "build:staging": "env-cmd -f .env.staging next build", "build": "next build", "start:dev": "env-cmd -f .env.dev next start", "start:staging": "env-cmd -f .env.staging next start", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^6.0.2", "@mui/material": "^6.0.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.16.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^4.1.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "embla-carousel-react": "^8.6.0", "env-cmd": "^10.1.0", "form-data": "^4.0.0", "input-otp": "^1.4.2", "jodit": "^4.2.27", "jodit-react": "^4.1.2", "lucide-react": "^0.525.0", "next": "^15.3.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-image-file-resizer": "^0.4.8", "react-infinite-scroll-component": "^6.1.0", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.0.2", "sonner": "^2.0.6", "styled-components": "^6.1.3", "sweetalert2": "^11.10.2", "swr": "^2.2.4", "tailwind-merge": "^2.3.0", "vaul": "^1.1.2", "zod": "^3.25.74", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^14.0.4", "@tailwindcss/postcss": "^4", "@types/axios": "^0.14.0", "@types/lodash": "^4.14.202", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "autoprefixer": "^10.0.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-unused-imports": "^3.0.0", "postcss": "^8", "prettier": "^3.2.4", "sass": "1.63.5", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "typescript": "^5"}}