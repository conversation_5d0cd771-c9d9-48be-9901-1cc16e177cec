import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  reactStrictMode: false,
  swcMinify: true,
  trailingSlash: false,
  compiler: {
    styledComponents: true,
  },
  images: {
    // domains: [
    //   'localhost',
    //   'dev.digiboxs.com',
    //   'digiboxs.com',
    //   'www.digiboxs.com',
    //   'cdn.digiboxs.com',
    //   'cdn.lucablock.io',
    // ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'dev-api.digiboxs.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.digiboxs.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.digiboxs.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
