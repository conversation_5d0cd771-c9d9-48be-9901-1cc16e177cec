export const validateNumber = (value: any, decimalPlaces = 0) => {
  // ถ้าค่าว่าง ให้ set เป็น empty string
  if (value === '') {
    return '';
  }

  // Remove commas for validation
  const cleanValue = String(value).replace(/,/g, '');

  // ตรวจสอบว่ามีเครื่องหมายลบหรือไม่
  if (cleanValue.includes('-')) {
    // ถ้ามีเครื่องหมายลบ ไม่อนุญาต
    return '';
  }

  // Allow just a decimal point (for typing ".")
  if (cleanValue === '.') {
    return decimalPlaces > 0 ? '.' : '';
  }

  // Regex patterns - more permissive for decimal input
  let validNumberRegex;
  if (decimalPlaces > 0) {
    // Allow: optional digits, optional decimal point, and up to specified decimal places
    // This allows: "1", "1.", "1.5", ".5", "0.", etc.
    validNumberRegex = new RegExp(`^(\\d*\\.?\\d{0,${decimalPlaces}})$`);
  } else {
    // For integer only, allow only digits
    validNumberRegex = /^\d*$/;
  }

  // ตรวจสอบรูปแบบตัวเลข
  if (validNumberRegex.test(cleanValue)) {
    return cleanValue;
  }

  // ถ้าไม่ผ่านการตรวจสอบ ให้ return empty string
  return '';
};

export const onKeyDown = (event: any) => {
  // บล็อคคีย์ "-" (minus sign) ทั้งใน main keyboard และ numpad
  if (event.key === '-' || event.key === 'Minus') {
    event.preventDefault();
  }

  // บล็อคคีย์ "e" และ "E" (scientific notation)
  if (event.key === 'e' || event.key === 'E') {
    event.preventDefault();
  }

  // บล็อคคีย์ "+" (plus sign)
  if (event.key === '+' || event.key === 'Plus') {
    event.preventDefault();
  }
};

// Format number with commas for display
export const formatNumberWithCommas = (value: any, decimalPlaces = 0) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }

  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  // Format with commas using Intl.NumberFormat
  return new Intl.NumberFormat('en-US', {
    maximumFractionDigits: decimalPlaces,
    minimumFractionDigits: 0,
  }).format(numValue);
};

// Format number with commas while preserving decimal input state
export const formatNumberWithCommasPreserveDecimal = (
  value: string,
  decimalPlaces = 0,
) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }

  // Handle special cases for partial decimal inputs
  if (value === '.') {
    return '.';
  }

  // Split into integer and decimal parts
  const parts = value.split('.');
  const integerPart = parts[0] || '';
  const decimalPart = parts[1];

  // Format the integer part with commas
  let formattedInteger = '';
  if (integerPart !== '') {
    const integerNum = Number(integerPart);
    if (!Number.isNaN(integerNum)) {
      formattedInteger = new Intl.NumberFormat('en-US').format(integerNum);
    } else {
      formattedInteger = integerPart;
    }
  }

  // Handle decimal part
  if (decimalPart !== undefined) {
    // Preserve the decimal part as-is, but limit to specified decimal places
    const limitedDecimalPart = decimalPart.substring(0, decimalPlaces);
    return `${formattedInteger}.${limitedDecimalPart}`;
  }

  // If value ends with '.', preserve it
  if (value.endsWith('.')) {
    return `${formattedInteger}.`;
  }

  return formattedInteger;
};

// Remove commas from formatted number to get clean numeric value
export const removeCommas = (value: string) => {
  return value.replace(/,/g, '');
};

export const inputNumberFormat = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  const value: number | any = validateNumber(event.target.value, digit);

  if (value !== null && value >= 0) {
    form.setValue(name, value);
  } else {
    form.setValue(name, 0.0);
  }
};

// Enhanced input number format with comma display
export const inputNumberFormatWithCommas = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  const inputValue = event.target.value;
  const cleanValue = validateNumber(inputValue, digit);

  if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
    // Set the actual numeric value for form submission
    form.setValue(name, cleanValue);
  } else if (cleanValue === '') {
    // Handle empty input
    form.setValue(name, '');
  } else {
    // Invalid input, prevent the change by setting to previous valid value
    const currentValue = form.getValues(name);
    if (currentValue !== undefined && currentValue !== '') {
      form.setValue(name, currentValue);
    } else {
      form.setValue(name, '');
    }
  }
};

// Get formatted display value for controlled inputs
export const getFormattedDisplayValue = (value: any, digit: number = 0) => {
  if (value === '' || value === null || value === undefined) {
    return '';
  }

  // Convert to string to preserve partial decimal inputs
  const stringValue = String(value);

  // If it's a partial decimal input (ends with . or has trailing zeros), preserve it
  if (stringValue.endsWith('.') || stringValue.includes('.')) {
    return formatNumberWithCommasPreserveDecimal(stringValue, digit);
  }

  return formatNumberWithCommas(value, digit);
};

// Create a controlled number input handler that maintains formatting
export const createNumberInputHandler = (
  form: any,
  name: any,
  digit: number,
) => {
  return {
    value: getFormattedDisplayValue(form.watch(name), digit),
    onChange: (event: any) => {
      const inputValue = event.target.value;
      const cleanValue = validateNumber(inputValue, digit);

      if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
        form.setValue(name, cleanValue);
      } else if (cleanValue === '') {
        form.setValue(name, '');
      }
      // For invalid input, don't update the form value
    },
    onPaste: (event: any) => {
      event.preventDefault();
      const pastedText = (
        event.clipboardData || (window as any).clipboardData
      ).getData('text');
      const cleanValue = validateNumber(pastedText, digit);

      if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
        form.setValue(name, cleanValue);
      } else if (cleanValue === '') {
        form.setValue(name, '');
      }
    },
  };
};

// Create a number input handler that works with React Hook Form's field object
export const createFieldNumberInputHandler = (field: any, digit: number) => {
  return {
    ...field,
    value: getFormattedDisplayValue(field.value, digit),
    onChange: (event: any) => {
      const inputValue = event.target.value;
      // Remove commas before validation
      const cleanInputValue = inputValue.replace(/,/g, '');
      const cleanValue = validateNumber(cleanInputValue, digit);

      // Allow the input if it's valid or if it's a partial decimal input
      if (cleanValue !== null && cleanValue !== '') {
        field.onChange(cleanValue);
      } else if (cleanValue === '') {
        field.onChange('');
      }
      // For invalid input, don't update the field value
    },
    onPaste: (event: any) => {
      event.preventDefault();
      const pastedText = (
        event.clipboardData || (window as any).clipboardData
      ).getData('text');
      const cleanValue = validateNumber(pastedText, digit);

      if (cleanValue !== null && cleanValue !== '') {
        field.onChange(cleanValue);
      } else if (cleanValue === '') {
        field.onChange('');
      }
    },
  };
};

// Handle paste events for number inputs with comma formatting
export const handleNumberPaste = (
  event: any,
  form: any,
  name: any,
  digit: number,
) => {
  event.preventDefault();

  // Get pasted text
  const pastedText = (
    event.clipboardData || (window as any).clipboardData
  ).getData('text');
  const cleanValue = validateNumber(pastedText, digit);

  if (cleanValue !== null && cleanValue !== '' && Number(cleanValue) >= 0) {
    // Set the actual numeric value for form submission
    form.setValue(name, cleanValue);
  } else if (cleanValue === '') {
    // Handle empty paste
    form.setValue(name, '');
  }
  // If invalid, do nothing (paste is prevented)
};
