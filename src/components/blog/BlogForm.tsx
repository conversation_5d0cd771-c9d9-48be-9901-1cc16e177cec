'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import 'react-quill/dist/quill.snow.css';
import styled from 'styled-components';
import { ChevronLeft } from 'lucide-react';
import GalleryList from '@/components/blog/GalleryList';
import { isEmpty, isUndefined } from 'lodash';

import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { MultiSelect } from '@/components/blog/MultiSelect';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useRouter } from 'next/navigation';

import Swal from 'sweetalert2';
import JoditEditor from 'jodit-react';
import blogService, { BlogFormType } from '@/services/blog';
import { ApiResponse } from '@/utils/types';
import { userStore } from '@/store/slices/user';

type Props = {
  action?: string;
  blogId?: number;
};
const emojiCategories: { [key: string]: string[] } = {
  smileys: [
    '😊',
    '😂',
    '😍',
    '😢',
    '😎',
    '😁',
    '😜',
    '😇',
    '🤩',
    '🥳',
    '🤗',
    '😱',
    '😷',
    '😃',
    '😄',
    '😆',
    '😉',
    '😋',
    '😏',
    '😒',
    '😞',
    '😔',
    '😖',
    '😡',
    '😠',
    '😤',
    '😩',
    '😫',
    '😭',
    '😓',
    '😢',
    '😲',
    '😵',
    '😳',
    '😧',
    '😦',
    '😮',
    '😬',
    '😣',
    '😥',
    '😢',
  ],
  gestures: [
    '👍',
    '🙌',
    '👏',
    '🙏',
    '👋',
    '🤝',
    '✌️',
    '🤟',
    '👌',
    '👊',
    '🤲',
    '👐',
    '💪',
    '✋',
    '🤚',
    '👆',
    '👇',
    '👉',
    '👈',
    '🖖',
    '🖐️',
    '🤟',
    '🤘',
    '✍️',
    '📝',
    '👂',
    '👃',
    '👀',
    '👁️',
    '🧠',
    '👅',
    '👄',
    '💋',
    '👶',
    '🧒',
    '👦',
    '👧',
    '🧑',
    '👩',
    '👨',
  ],
  animals: [
    '🐶',
    '🐱',
    '🐭',
    '🐹',
    '🐰',
    '🦊',
    '🐻',
    '🐼',
    '🐨',
    '🐯',
    '🦁',
    '🐮',
    '🐷',
    '🐸',
    '🐵',
    '🐔',
    '🐧',
    '🐦',
    '🐤',
    '🐣',
    '🐥',
    '🦆',
    '🦅',
    '🦉',
    '🦇',
    '🐺',
    '🐗',
    '🐴',
    '🦄',
    '🐝',
    '🐛',
    '🦋',
    '🐌',
    '🐞',
    '🐜',
    '🕷️',
    '🦂',
    '🦀',
    '🦞',
    '🦐',
    '🦑',
  ],
  foods: [
    '🍎',
    '🍌',
    '🍓',
    '🍕',
    '🍔',
    '🍟',
    '🍣',
    '🍤',
    '🍦',
    '🍩',
    '🍪',
    '🎂',
    '🍰',
    '🍫',
    '🍿',
    '🍬',
    '🍭',
    '🍮',
    '🍯',
    '🍜',
    '🍝',
    '🍠',
    '🍢',
    '🍣',
    '🍥',
    '🍡',
    '🍦',
    '🍧',
    '🍨',
    '🍩',
    '🍪',
    '🎂',
    '🍰',
    '🍫',
    '🍿',
    '🍩',
    '🍪',
    '🎂',
    '🍰',
    '🍫',
  ],
};

const FormSchema = z.object({
  title: z
    .string({
      required_error: 'กรุณากรอกชื่อบทความ',
    })
    .min(1, 'กรุณากรอกชื่อบทความ'),
  description: z
    .string({
      required_error: 'กรุณากรอกรายละเอียด',
    })
    .min(1, 'กรุณากรอกรายละเอียด'),
  keyword: z
    .string({
      required_error: 'กรุณากรอกคีย์เวิร์ด',
    })
    .min(1, 'กรุณากรอกคีย์เวิร์ด'),
  content: z.string({
    required_error: 'กรุณาระบุเนื้อหาบทความ',
  }),
  type: z.string({
    required_error: 'กรุณาเลือกประเภทบทความ',
  }),
  tags: z
    .string({
      required_error: 'กรุณาเลือกหมวดหมู่บทความ',
    })
    .array()
    .min(1, 'กรุณาเลือกหมวดหมู่บทความ')
    .optional()
    .refine((val) => val && val.length > 0, {
      message: 'กรุณาเลือกหมวดหมู่บทความ',
    }),
  urlSlug: z
    .string({
      required_error: 'กรุณากรอก URL Slug',
    })
    .min(1, 'กรุณากรอก URL Slug'),
  thumbnailUrl: z
    .string({
      required_error: 'กรุณากรอก URL รูปปก',
    })
    .min(1, 'กรุณากรอก URL รูปปก'),
  status: z.enum(['false', 'true'], {
    required_error: 'กรุณาเลือกสถานะ',
  }),
});

const EditorContainerStyle = styled.div`
  width: 100%;
  flex: 1;
  position: relative;
  overflow: hidden;
  height: calc(100vh - 150px);

  .quill {
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #eee;
    .ql-toolbar {
      border-width: 0 0 1px 0;
      border-bottom: 1px solid #ccc;
    }
    .ql-container {
      flex: 1;
      height: 300px;
      overflow-y: auto;
      border-width: 0 0 1px 0;
      border-bottom: 1px solid #ccc;
      .ql-editor {
        background: white;
        height: 100%;
        p {
          font-size: 14px;
        }
      }
    }
  }
`;

const BlogForm = ({ action, blogId }: Props) => {
  const { push } = useRouter();
  const author = userStore((state) => state.me);
  const [isShowGallery, setShowGallery] = useState(false);
  const [typeList, setTypeList] = useState<any[]>([]);
  const [categoryList, setCategoryList] = useState<any[]>([]);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });
  const editorRef = useRef<any>(null);

  const options = [
    'paragraph',
    'bold',
    'fontsize',
    'font',
    'ul',
    'ol',
    'align',
    'underline',
    'image',
    'link',
    'italic',
    'table',
    'hr',
    'print',
  ];

  const initialConfig = {
    readonly: false,
    placeholder: '',
    defaultActionOnPaste: 'insert_as_html',
    defaultLineHeight: 1.5,
    enter: 'p',
    buttons: options,
    buttonsMD: options,
    buttonsSM: options,
    buttonsXS: options,
    statusbar: false,
    toolbar: true,
    triggerChangeEvent: true,
    toolbarAdaptive: true,
    toolbarSticky: false,
    showCharsCounter: true,
    showWordsCounter: true,
    showXPathInStatusbar: false,
    useSearch: true,
    height: 'calc(100dvh - 200px)',
    resizable: false,
    extraButtons: [
      {
        name: 'emoji',
        tooltip: 'Emoji',
        iconURL: '/icons/icon-emoji.svg',
        exec: function (editor: any) {
          const emojiMenu = document.createElement('div');
          emojiMenu.classList.add('emoji-wrapper');

          const closeEmojiMenu = (event: MouseEvent) => {
            if (
              !emojiMenu.contains(event.target as Node) &&
              editor.container.contains(emojiMenu)
            ) {
              editor.container.removeChild(emojiMenu);
              document.removeEventListener('click', closeEmojiMenu);
            }
          };

          const existingMenu = editor.container.querySelector('.emoji-wrapper');
          if (existingMenu) {
            editor.container.removeChild(existingMenu);
            document.removeEventListener('click', closeEmojiMenu);
            return;
          }

          const tabs = document.createElement('div');
          tabs.classList.add('emoji-tabs');
          emojiMenu.appendChild(tabs);

          const emojiContent = document.createElement('div');
          emojiContent.classList.add('emoji-grid');
          emojiMenu.appendChild(emojiContent);

          Object.keys(emojiCategories).forEach((category, index) => {
            const tabButton = document.createElement('button');
            tabButton.type = 'button';
            tabButton.innerText = category;

            if (index === 0) {
              tabButton.style.boxShadow = 'inset 0px -4px 2px -2px black';
            }

            tabButton.onclick = () => {
              const activeTab: any = tabs.querySelector('[data-active="true"]');
              if (activeTab) {
                activeTab.style.boxShadow = 'none';
                activeTab.removeAttribute('data-active');
              }

              tabButton.style.boxShadow = 'inset 0px -4px 2px -2px black';
              tabButton.setAttribute('data-active', 'true');

              emojiContent.innerHTML = '';
              emojiCategories[category]?.forEach((emoji) => {
                const emojiButton = document.createElement('button');
                emojiButton.innerText = emoji;
                emojiButton.onclick = () => {
                  editor.selection.insertHTML(emoji);
                  if (editor.container.contains(emojiMenu)) {
                    editor.container.removeChild(emojiMenu);
                  }
                  document.removeEventListener('click', closeEmojiMenu);
                };
                emojiContent.appendChild(emojiButton);
              });
            };

            tabs.appendChild(tabButton);
          });

          tabs.querySelector('button')!.click();

          editor.container.appendChild(emojiMenu);

          setTimeout(() => {
            document.addEventListener('click', closeEmojiMenu);
          }, 0);
        },
      },
    ],
    events: {
      afterInit: async (editor: any) => {
        editorRef.current = editor;
      },
    },
  };
  const [config] = useState<any>(initialConfig);

  const fetchData = async () => {
    const blogTypes: ApiResponse = await blogService.getBlogTypes();
    if (blogTypes.status) {
      setTypeList(blogTypes.data);
    }

    const blogCategories: ApiResponse = await blogService.getBlogCategories();
    if (blogCategories.status) {
      const setValue = await blogCategories.data.map((category: any) => {
        return {
          id: category.id,
          value: category.name,
        };
      });
      setCategoryList(setValue);
    }
  };

  const fetchBlog = async () => {
    const res: ApiResponse = await blogService.getBlogById(Number(blogId));
    if (res.status) {
      const blog = res.data;

      form.setValue('title', blog.title);
      form.setValue('content', blog.content);
      form.setValue('keyword', blog.keyword);
      form.setValue('type', blog.blogType.name);
      form.setValue(
        'tags',
        blog.categories.map((category: any) => category.name),
      );
      form.setValue('urlSlug', blog.urlSlug);
      form.setValue('thumbnailUrl', blog.thumbnailUrl);
      form.setValue('description', blog.description);
      form.setValue('status', blog.isPublished ? 'true' : 'false');
    }
  };

  useEffect(() => {
    form.reset();
    fetchData();
    form.setValue('status', 'false');

    if (action === 'edit') {
      fetchBlog();
    }
  }, []);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const type = typeList.find((type) => type.name === values.type);
    const tags: number[] = (values.tags || []).map((tag: string) => {
      const category = categoryList
        .filter((category) => category.value === tag)
        .map((category) => category.id);
      return category[0];
    });

    const req: BlogFormType = {
      title: values.title,
      content: values.content,
      categories: tags,
      blogTypeId: type.id,
      keyword: values.keyword,
      isPublished: values.status === 'true',
      thumbNailUrl: values.thumbnailUrl,
      urlSlug: values.urlSlug,
      description: values.description,
      author: author.name,
    };

    const res: ApiResponse =
      action !== 'edit'
        ? await blogService.createBlog(req)
        : await blogService.updateBlog(Number(blogId), req);
    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/blog');
      });
    }
  };

  const insertImage = (imageDom: string) => {
    form.setValue(
      'content',
      `${!isUndefined(form.watch('content')) ? form.getValues('content') : ''} ${imageDom}`,
    );
  };

  return (
    <div className="pt-[40px]">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-row flex-wrap gap-4">
            <EditorContainerStyle>
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem className="w-full h-full">
                    <FormControl>
                      <JoditEditor
                        value={field.value}
                        config={config}
                        onBlur={() => {
                          field.onChange(editorRef.current.value);
                        }}
                        onChange={(newContent: any) => {
                          if (newContent !== field.value) {
                            field.onChange(newContent);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage className="z-50 absolute top-20 left-5" />
                  </FormItem>
                )}
              />
            </EditorContainerStyle>
            <div className="w-[400px] max-w-full sm:w-[300px] px-[10px] relative">
              <div className="flex flex-row justify-between items-center">
                <div>
                  {isShowGallery ? (
                    <p
                      onClick={() => setShowGallery(false)}
                      className="cursor-pointer flex flex-row items-center gap-1"
                    >
                      <ChevronLeft /> Back
                    </p>
                  ) : (
                    <p className="font-[500]">Blog Info</p>
                  )}
                </div>
                {!isShowGallery && (
                  <div>
                    <p
                      className="text-[1em] underline cursor-pointer"
                      onClick={() => setShowGallery(true)}
                    >
                      Open Gallery
                    </p>
                  </div>
                )}
              </div>
              <div className="relative">
                {isShowGallery && (
                  <GalleryList
                    handleInsertImage={(imageDom: string) =>
                      insertImage(imageDom)
                    }
                  />
                )}
              </div>
              <div className={`form-data ${isShowGallery ? 'hidden' : ''}`}>
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ชื่อบทความ"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Description</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="รายละเอียด"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="keyword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Keyword</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="คีย์เวิร์ด"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="เลือก type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {!isEmpty(typeList) &&
                            typeList.map((type, index) => (
                              <SelectItem key={index} value={type.name || null}>
                                {type.name || null}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Tags</FormLabel>
                      <FormControl>
                        <MultiSelect
                          options={categoryList}
                          onValueChange={field.onChange}
                          defaultValue={field.value || []}
                          placeholder="เลือก tags"
                          variant="inverted"
                          animation={2}
                          maxCount={1}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="urlSlug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Url Slug</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="url slug"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="thumbnailUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text--label">Url รูปปก</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="url รูปปก"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          defaultValue={field.value}
                        >
                          <div className="flex flex-row justify-start items-center gap-4 p-2">
                            <FormItem>
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  ฉบับร่าง
                                </FormLabel>
                              </div>
                            </FormItem>
                            <FormItem>
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  เผยแพร่
                                </FormLabel>
                              </div>
                            </FormItem>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="pt-4 pb-[8em] flex flex-row gap-4 items-center justify-center">
                  <Button type="submit" className="w-full">
                    บันทึก
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default BlogForm;
