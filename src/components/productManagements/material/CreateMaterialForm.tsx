'use client';

import React, { useEffect, useState } from 'react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse, ValidateFileType } from '@/utils/types';
import { validateFile } from '@/utils/validateFiles';
import { isEmpty, isNull, isUndefined } from 'lodash';
import fileUploadService from '@/services/fileUploader';
import { Toast } from '@/utils/toast';
import ImageUploadComponent from '@/components/shared/image-upload';
import { useRouter } from 'next/navigation';
import { Label } from '@/components/ui/label';
import Swal from 'sweetalert2';
import materialService, { MaterialRequest } from '@/services/material';
import gramService from '@/services/gram';
import { ActionType } from '../../../enum/action';
import CreateGramModal from '../gram/modal/CreateGramModal';
import { MultiSelect } from '@/components/blog/MultiSelect';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';

type PropType = {
  action: string;
  id?: number;
};

const FormSchema = z.object({
  name: z
    .string({
      required_error: 'กรุณากรอกชื่อวัสดุ',
    })
    .min(1, 'กรุณากรอกชื่อวัสดุ'),
  grams: z
    .array(z.string().min(1, 'แต่ละค่าต้องไม่เป็นค่าว่าง'))
    .min(1, 'กรุณาเลือกความหนาอย่างน้อย 1 รายการ')
    .optional()
    .refine((val) => val && val.length > 0, {
      message: 'กรุณาเลือกความหนาอย่างน้อย 1 รายการ',
    }),
  image: z
    .string({
      required_error: 'กรุณาอัปโหลดรูปภาพ',
    })
    .min(1, 'กรุณาอัปโหลดรูปภาพ'),
});

const CreateMaterialForm = ({ action, id }: PropType) => {
  const { push } = useRouter();
  const [fileUpload, setFileUpload] = useState<any>(null);
  const [material, setMaterial] = useState<any>(null);
  const [grams, setGrams] = useState<any>([]);
  const [selectedGramIds, setSelectedGramIds] = useState<number[]>([]);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [gramModalAction, setGramModalAction] = useState<string>(
    ActionType.CREATE,
  );
  const [editId, setEditId] = useState<any>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
  });

  const getMaterialById = async (id: number) => {
    const res: ApiResponse = await materialService.getMaterialById(id);
    if (res.status) {
      form.setValue('name', res.data.name);
      form.setValue('image', res.data.imageUrl);

      // Store the selected gram IDs for later reference
      const selectedIds = res.data.grams.map((item: any) => item.id);
      setSelectedGramIds(selectedIds);

      form.setValue(
        'grams',
        res.data.grams.map((item: any) => `${item.gsm} แกรม  ${item.mm} mm.`),
      );

      setFileUpload(res.data.imageUrl);
      setMaterial(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  const getMaterialGrams = async () => {
    const res: ApiResponse = await gramService.getGrams();
    if (res.status) {
      const setValue = await res.data.map((item: any) => {
        return {
          id: item.id,
          value: `${item.gsm} แกรม  ${item.mm} mm.`,
        };
      });
      setGrams(setValue);

      // If we have selected gram IDs (from editing), update the form values to match the new gram values
      if (selectedGramIds.length > 0) {
        const updatedSelectedValues = selectedGramIds
          .map((id) => {
            const gram = setValue.find((item: any) => item.id === id);
            return gram ? gram.value : null;
          })
          .filter((value) => value !== null);

        form.setValue('grams', updatedSelectedValues);
      } else {
        form.setValue('grams', []);
      }
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    form.reset();
    setFileUpload(null);
    setSelectedGramIds([]);

    getMaterialGrams();

    if (action === 'edit' && !isUndefined(id)) {
      getMaterialById(id);
    }
  }, [action, id]);

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    // Use selectedGramIds directly for more reliable mapping
    const gramList: number[] =
      selectedGramIds.length > 0 ? selectedGramIds : [];

    // Fallback to value-based mapping if selectedGramIds is empty (for new selections)
    if (
      gramList.length === 0 &&
      !isEmpty(values.grams) &&
      !isUndefined(values.grams)
    ) {
      values.grams.forEach((select) => {
        const find = grams.find((item: any) => item.value === select);
        if (!isUndefined(find)) {
          gramList.push(find.id);
        }
      });
    }

    const req: MaterialRequest = {
      name: values.name,
      imageUrl: values.image,
      grams: gramList,
    };

    if (action === 'edit') {
      req.isActive = material.isActive;
    }

    const res: ApiResponse =
      action !== 'edit'
        ? await materialService.createMaterial(req)
        : await materialService.updateMaterial(material.id, req);

    if (!res.status) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then(() => {
        push('/material');
      });
    }
  };

  const onEditGram = async (id: number) => {
    setGramModalAction(ActionType.EDIT);
    setOpenModal(true);
    setEditId(id);
  };

  const onDeleteGram = async (id: number) => {
    Swal.fire({
      title: 'ลบความหนา!',
      text: 'คุณต้องการลบความหนานี้ใช่หรือไม่?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'ลบ',
      cancelButtonText: 'ยกเลิก',
      confirmButtonColor: '#000000',
      cancelButtonColor: '#DAE2E5',
    }).then(async (result) => {
      if (result.isConfirmed) {
        const res = await gramService.deleteGram(id);
        if (res.status === false) {
          Swal.fire({
            title: 'เกิดข้อผิดพลาด',
            text: res.message,
            icon: 'error',
            showConfirmButton: true,
            confirmButtonColor: '#000000',
            confirmButtonText: 'OK',
          }).then();
        } else {
          Swal.fire({
            title: 'สำเร็จ',
            text: res.message,
            icon: 'success',
            showConfirmButton: true,
            confirmButtonColor: '#000000',
            confirmButtonText: 'OK',
          }).then();
          await getMaterialGrams();
        }
      }
    });
  };

  const handleImageChange = async (e: any) => {
    const file = e.target.files[0];

    const validate: ValidateFileType = validateFile(e.target.files);
    if (validate.status) {
      if (!isUndefined(file) && !isNull(file)) {
        const formData: FormData = new FormData();
        formData.append('file', file);

        const res: ApiResponse = await fileUploadService.upload(formData);

        if (res.status) {
          const url = res.data;
          setFileUpload(url);
          form.setValue('image', url);
        } else {
          Toast.fire({
            title: 'เกิดข้อผิดพลาด',
            text: `ไฟล์อัปโหลดผิดพลาด :${res.message}`,
            icon: 'error',
          });
        }
      }
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: validate.message,
        icon: 'error',
      });
    }
  };

  return (
    <>
      <div className="w-full h-full flex items-center justify-center">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-[40px] w-[800px]">
              <h3 className="text-[40px] text-[#000] font-bold">
                {action !== 'edit' ? 'สร้างวัสดุ' : 'แก้ไขวัสดุ'}
              </h3>
              <div className="flex flex-col gap-[39px]">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <Label>ชื่อ</Label>
                      <FormControl>
                        <Input
                          placeholder="ชื่อวัสดุ"
                          className="resize-none"
                          {...field}
                          defaultValue={field.value || ''}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="grams"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex flex-row items-center justify-between">
                        <FormLabel className="text--label">ความหนา</FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-[#0050FF]"
                          onClick={() => {
                            setOpenModal(true);
                            setGramModalAction(ActionType.CREATE);
                          }}
                        >
                          +เพิ่ม
                        </Button>
                      </div>
                      <FormControl>
                        <MultiSelect
                          options={grams}
                          onValueChange={(values) => {
                            field.onChange(values);
                            // Update selectedGramIds when values change
                            const ids = values
                              .map((value) => {
                                const gram = grams.find(
                                  (item: any) => item.value === value,
                                );
                                return gram ? gram.id : null;
                              })
                              .filter((id) => id !== null);
                            setSelectedGramIds(ids);
                          }}
                          defaultValue={field.value || []}
                          placeholder="เลือกแกรม"
                          variant="inverted"
                          animation={2}
                          maxCount={grams.length}
                          {...field}
                        >
                          {(option: any) => (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  aria-haspopup="true"
                                  size="icon"
                                  variant="ghost"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Toggle menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>จัดการ</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => onEditGram(option.id)}
                                >
                                  แก้ไข
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => onDeleteGram(option.id)}
                                >
                                  ลบ
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </MultiSelect>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div>
                  <Label>รูปภาพ</Label>
                  <ImageUploadComponent file={fileUpload}>
                    <FormField
                      control={form.control}
                      name="image"
                      render={() => (
                        <FormItem>
                          <input
                            type="file"
                            onChange={handleImageChange}
                            className="w-[140px] resize-none"
                          />
                          <FormMessage className="absolute top-[117px]" />
                        </FormItem>
                      )}
                    />
                  </ImageUploadComponent>
                </div>
              </div>
              <div className="flex flex-row items-center justify-between gap-[16px] w-full h-full">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => push('/material')}
                >
                  ยกเลิก
                </Button>
                <Button type="submit" className="w-full">
                  บันทึก
                </Button>
              </div>
            </div>
          </form>
        </Form>
        <CreateGramModal
          openModal={openModal}
          setOpenModal={(value) => setOpenModal(value)}
          fetchData={getMaterialGrams}
          action={gramModalAction}
          id={editId}
        />
      </div>
    </>
  );
};

export default CreateMaterialForm;
