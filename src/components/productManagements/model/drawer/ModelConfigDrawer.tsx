'use client';

import * as React from 'react';
import { useEffect, useState } from 'react';

import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  Drawer<PERSON>ooter,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  CircleMinus,
  MoreHorizontal,
  PencilLine,
  Plus,
  Trash2,
  X,
} from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';

import { Input } from '@/components/ui/input';
import { createFieldNumberInputHandler, onKeyDown } from '@/utils/input-number';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ApiResponse } from '@/utils/types';
import { Toast } from '@/utils/toast';
import { MultiSelect } from '@/components/blog/MultiSelect';
import printingConfigService, {
  ModelSizeConfigDetail,
  PrintingConfigRequest,
} from '@/services/printingConfig';
import { isEmpty, isUndefined } from 'lodash';
import coatingService from '@/services/coating';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import PrintingModal from '@/components/productManagements/model/modal/PrintingModal';
import printingService from '@/services/printing';
import Swal from 'sweetalert2';

type Props = {
  setOpenModal: (e: boolean) => void;
  openModal: boolean;
  modelSizeConfigId: number;
  detail: string;
  fetchData: (id: number) => void;
  modelId: number;
};

const PriceConfigSchema = z.object({
  amount: z.coerce
    .number({
      required_error: 'กรุณากรอกจำนวน',
      invalid_type_error: 'กรุณากรอกจำนวน',
    })
    .gte(1, 'จำนวนเริ่มต้น 1 ชิ้น'),
  price: z.coerce
    .number({
      required_error: 'กรุณากรอกราคา',
      invalid_type_error: 'กรุณากรอกราคา',
    })
    .gte(0.1, 'ราคาเริ่มต้น 0.1 บาท'),
  period: z.coerce
    .number({
      required_error: 'กรุณากรอกระยะเวลา',
      invalid_type_error: 'กรุณากรอกระยะเวลา',
    })
    .gte(1, 'ระยะเวลาเริ่มต้น 1 วัน'),
  coatings: z
    .array(z.string().min(1, 'แต่ละค่าต้องไม่เป็นค่าว่าง'))
    .min(1, 'เลือก 1 รายการ')
    .optional()
    .refine((val) => val && val.length > 0, {
      message: 'เลือก 1 รายการ',
    }),
});

const PrintingSchema = z.object({
  printingId: z.string().optional(),
  priceConfig: z
    .array(PriceConfigSchema)
    .min(1, 'ต้องกำหนดราคาอย่างน้อย 1 รายการ'),
});

const FormSchema = z.object({
  printingConfigs: z
    .array(PrintingSchema)
    .min(1, 'ต้องกำหนดระบบพิมพ์อย่างน้อย 1 รายการ'),
});

const ModelConfigDrawer = ({
  setOpenModal,
  openModal,
  modelSizeConfigId,
  detail,
  fetchData,
  modelId,
}: Props) => {
  const [printingConfigs, setPrintingConfigs] = useState<any>([]);
  const [coatingList, setCoatingList] = useState<any>([]);
  const [printings, setPrintings] = useState<any>([]);
  const [openPrinting, setOpenPrinting] = useState<boolean>(false);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      printingConfigs: [],
    },
  });

  const getPrintingConfigByModelSizeConfigId = async (
    modelSizeConfigId: number,
  ) => {
    const res: ApiResponse =
      await printingConfigService.getPrintingConfigByModelSizeConfigId(
        modelSizeConfigId,
      );
    if (res.status) {
      setPrintingConfigs(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  const getCoatingList = async () => {
    const res: ApiResponse = await coatingService.getSelectCoating();
    if (res.status) {
      const setValue = await res.data.content.map((item: any) => {
        return {
          id: item.id,
          value: item.name,
        };
      });
      setCoatingList(setValue);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  const getAllPrinting = async () => {
    const res: ApiResponse = await printingService.getPrinting();
    if (res.status) {
      setPrintings(res.data);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    if (openModal) {
      form.reset();

      if (!isUndefined(modelSizeConfigId)) {
        getPrintingConfigByModelSizeConfigId(modelSizeConfigId);
        getCoatingList();
        getAllPrinting();
      }
    }
  }, [openModal, modelSizeConfigId]);

  useEffect(() => {
    form.reset();
    if (
      !isEmpty(printingConfigs) &&
      !isEmpty(coatingList) &&
      !isEmpty(printings)
    ) {
      printingConfigs.forEach((item: any) => {
        const printing: any = printings.find(
          (printing: any) => printing.id === item.printing.id,
        );
        if (!isUndefined(printing)) {
          form.setValue('printingConfigs', [
            ...form.watch('printingConfigs'),
            {
              printingId: printing.name,
              priceConfig: item.modelSizeConfigDetail.map((config: any) => {
                return {
                  amount: config.amount,
                  price: config.price,
                  period: config.period,
                  coatings: config.modelSizeConfigCoating.map(
                    (coating: any) => {
                      const find: any = coatingList.find(
                        (item: any) => item.id === coating.coating.id,
                      );
                      if (!isUndefined(find)) {
                        return find.value;
                      }
                      return '';
                    },
                  ),
                };
              }),
            },
          ]);
        }
      });
    }
  }, [printingConfigs, coatingList, printings]);

  const handlerAddPrinting = () => {
    setOpenPrinting(true);
  };

  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    const req: PrintingConfigRequest[] = [];

    values.printingConfigs.forEach((item: any) => {
      const printingConfig: PrintingConfigRequest = {
        printingId: 0,
        modelSizeConfigDetail: [],
      };

      const find: any = printings.find(
        (printing: any) => printing.name === item.printingId,
      );
      if (!isUndefined(find)) {
        printingConfig.printingId = find.id;
      }

      item.priceConfig.forEach((config: any) => {
        const modelSizeConfigDetail: ModelSizeConfigDetail = {
          amount: config.amount,
          period: config.period,
          price: config.price,
          modelSizeConfigCoating: [],
        };

        const modelSizeConfigCoating: number[] = [];
        config.coatings.forEach((coating: any) => {
          const find: any = coatingList.find(
            (item: any) => item.value === coating,
          );
          if (!isUndefined(find)) {
            modelSizeConfigCoating.push(find.id);
          }
        });

        modelSizeConfigDetail.modelSizeConfigCoating = modelSizeConfigCoating;

        printingConfig.modelSizeConfigDetail.push(modelSizeConfigDetail);
      });

      req.push(printingConfig);
    });

    const res: ApiResponse = await printingConfigService.updatePrintingConfig(
      modelSizeConfigId,
      req,
    );

    if (!res.status) {
      setOpenModal(false);
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      fetchData(modelId);
      setOpenModal(false);
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then();
    }
  };

  const handlerAddPrintingConfig = (printings: string[]) => {
    const currentConfigs = form.watch('printingConfigs');
    const duplicates: string[] = [];
    const newConfigs: any[] = [];

    printings.forEach((printing) => {
      const check = currentConfigs.find(
        (item: any) => item.printingId === printing,
      );
      if (!isUndefined(check)) {
        duplicates.push(printing);
      } else {
        newConfigs.push({
          printingId: printing,
          priceConfig: [
            {
              amount: 0,
              price: 0,
              period: 0,
              coatings: [],
            },
          ],
        });
      }
    });

    if (duplicates.length > 0) {
      Swal.fire({
        title: 'คำเตือน',
        text: `ไม่สามารถเพิ่มระบบพิมพ์ซ้ำกันได้: ${duplicates.join(', ')}`,
        icon: 'warning',
        showCloseButton: false,
        showCancelButton: false,
        showConfirmButton: false,
        reverseButtons: true,
        timer: 2000,
        timerProgressBar: true,
      }).then();
    }

    if (newConfigs.length > 0) {
      form.setValue('printingConfigs', [...currentConfigs, ...newConfigs]);
    }
  };

  const handlerChangePrintingMethod = (
    currentIndex: number,
    newPrintingName: string,
  ) => {
    const currentConfigs = form.watch('printingConfigs');

    // Check if the new printing method is already in use
    const isDuplicate = currentConfigs.some(
      (config: any, index: number) =>
        index !== currentIndex && config.printingId === newPrintingName,
    );

    if (isDuplicate) {
      Swal.fire({
        title: 'คำเตือน',
        text: 'ระบบพิมพ์นี้ถูกใช้งานแล้ว',
        icon: 'warning',
        showCloseButton: false,
        showCancelButton: false,
        showConfirmButton: false,
        reverseButtons: true,
        timer: 1500,
        timerProgressBar: true,
      }).then();
      return;
    }

    // Update the printing method
    const updatedConfigs = currentConfigs.map((config: any, index: number) => {
      if (index === currentIndex) {
        return {
          ...config,
          printingId: newPrintingName,
        };
      }
      return config;
    });

    form.setValue('printingConfigs', updatedConfigs);
  };

  const handlerRemovePrintingConfig = async (index: number) => {
    const printingConfigs: any[] = form.getValues('printingConfigs');
    const updatedConfigs: any[] = printingConfigs.filter((_, i) => i !== index);
    form.setValue('printingConfigs', [...updatedConfigs]);
  };

  const handleRemovePriceConfig = async (index: number, index2: number) => {
    const printingConfigs = form.getValues('printingConfigs');
    const priceConfig = printingConfigs[index];

    const updatedConfigs: any[] = printingConfigs.map(
      (item: any, i: number) => {
        if (i === index) {
          return {
            printingId: priceConfig.printingId,
            priceConfig: priceConfig.priceConfig.filter((_, i) => i !== index2),
          };
        }
        return item;
      },
    );

    form.setValue('printingConfigs', [...updatedConfigs]);
  };

  return (
    <>
      <Drawer
        open={openModal}
        direction="right"
        onOpenChange={() => setOpenModal(false)}
        modal
      >
        <DrawerTrigger asChild></DrawerTrigger>
        <DrawerContent style={{ width: '35vw', maxWidth: 'none' }}>
          <div>
            <DrawerHeader>
              <div className="flex flex-row items-center justify-between">
                <DrawerTitle>ตั้งค่าการพิมพ์</DrawerTitle>
                <DrawerClose asChild>
                  <X className="h-5 w-5 transition-transform duration-300 hover:rotate-90" />
                </DrawerClose>
              </div>
            </DrawerHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <div className=" flex flex-col gap-[24px]">
                  <div className="flex flex-row items-center justify-between px-[24px]">
                    <Label>{detail || ''}</Label>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handlerAddPrinting()}
                    >
                      <Plus /> เพิ่มการพิมพ์
                    </Button>
                  </div>
                  <div className="w-full max-h-[78vh] overflow-y-auto">
                    {!isEmpty(form.watch('printingConfigs')) &&
                      form
                        .watch('printingConfigs')
                        .map((item: any, index: number) => (
                          <div
                            key={
                              item.printingId ||
                              `config-${item.printingId}-${index}`
                            }
                            className="border-y overflow-hidden"
                          >
                            <div className="flex flex-row gap-[16px]  items-center flex-nowrap w-full px-[24px] py-[16px]">
                              <FormField
                                control={form.control}
                                name={`printingConfigs.${index}.printingId`}
                                render={({ field }) => (
                                  <Label htmlFor="printingId">
                                    {`${field.value} (${form.watch(`printingConfigs.${index}.priceConfig`).length})`}
                                  </Label>
                                )}
                              />
                              <div className="flex-1/2 flex flex-row items-center justify-end gap-[8px]">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="icon"
                                  onClick={() =>
                                    form.setValue(
                                      `printingConfigs.${index}.priceConfig`,
                                      [
                                        ...form.watch(
                                          `printingConfigs.${index}.priceConfig`,
                                        ),
                                        {
                                          amount: 0,
                                          price: 0,
                                          period: 0,
                                          coatings: [],
                                        },
                                      ],
                                    )
                                  }
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      type="button"
                                      aria-haspopup="true"
                                      size="icon"
                                      variant="outline"
                                    >
                                      <MoreHorizontal className="h-4 w-4" />
                                      <span className="sr-only">
                                        Toggle menu
                                      </span>
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuSub>
                                      <DropdownMenuSubTrigger>
                                        <PencilLine className="h-4 w-4 mr-2" />
                                        เปลี่ยนระบบพิมพ์
                                      </DropdownMenuSubTrigger>
                                      <DropdownMenuSubContent>
                                        {!isEmpty(printings) &&
                                          printings
                                            .filter(
                                              (printing: any) =>
                                                printing.name !==
                                                item.printingId,
                                            )
                                            .map(
                                              (
                                                printing: any,
                                                printingIndex: number,
                                              ) => (
                                                <DropdownMenuItem
                                                  key={printingIndex}
                                                  onClick={() =>
                                                    handlerChangePrintingMethod(
                                                      index,
                                                      printing.name,
                                                    )
                                                  }
                                                >
                                                  {printing.name}
                                                </DropdownMenuItem>
                                              ),
                                            )}
                                        {isEmpty(
                                          printings.filter(
                                            (printing: any) =>
                                              printing.name !== item.printingId,
                                          ),
                                        ) && (
                                          <DropdownMenuItem disabled>
                                            ไม่มีระบบพิมพ์อื่นให้เลือก
                                          </DropdownMenuItem>
                                        )}
                                      </DropdownMenuSubContent>
                                    </DropdownMenuSub>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handlerRemovePrintingConfig(index)
                                      }
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      ลบ
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                            <div className="flex flex-row gap-[16px] g items-center flex-nowrap w-full bg-[#F5F5F5] py-[11px] text-[12px] px-[24px]">
                              <div className="flex-1/2">จำนวน (ชิ้น)</div>
                              <div className="flex-1/2">ราคา (บาท/ชิ้น)</div>
                              <div className="flex-1/2">ระยะเวลาผลิต (วัน)</div>
                              <div className="flex-1/2">การเคลือบ</div>
                              <div className="flex-1/4 flex justify-end">
                                ลบ
                              </div>
                            </div>
                            <div className="flex flex-col gap-[8px] py-[8px]">
                              {form
                                .watch(`printingConfigs.${index}.priceConfig`)
                                .map((prices: any, index2: number) => (
                                  <div
                                    key={
                                      prices * index2 ||
                                      `print-config-${prices}-${(index2 + 1) * 2}`
                                    }
                                    className="flex flex-row gap-[8px] items-start flex-nowrap w-full px-[24px]"
                                  >
                                    <div className="flex-1/4">
                                      <FormField
                                        control={form.control}
                                        name={`printingConfigs.${index}.priceConfig.${index2}.amount`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormControl>
                                              <Input
                                                type="text"
                                                className="resize-none"
                                                placeholder="0"
                                                {...createFieldNumberInputHandler(
                                                  field,
                                                  2,
                                                )}
                                                onKeyDown={onKeyDown}
                                                onWheel={(e) =>
                                                  e.preventDefault()
                                                }
                                              />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                    <div className="flex-1/4">
                                      <FormField
                                        control={form.control}
                                        name={`printingConfigs.${index}.priceConfig.${index2}.price`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormControl>
                                              <Input
                                                type="text"
                                                className="resize-none"
                                                placeholder="0"
                                                {...createFieldNumberInputHandler(
                                                  field,
                                                  2,
                                                )}
                                                onKeyDown={onKeyDown}
                                                onWheel={(e) =>
                                                  e.preventDefault()
                                                }
                                              />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                    <div className="flex-1/4">
                                      <FormField
                                        control={form.control}
                                        name={`printingConfigs.${index}.priceConfig.${index2}.period`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormControl>
                                              <Input
                                                type="text"
                                                className="resize-none"
                                                placeholder="0"
                                                {...createFieldNumberInputHandler(
                                                  field,
                                                  2,
                                                )}
                                                onKeyDown={onKeyDown}
                                                onWheel={(e) =>
                                                  e.preventDefault()
                                                }
                                              />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                    <div className="flex-1/4">
                                      <FormField
                                        control={form.control}
                                        name={`printingConfigs.${index}.priceConfig.${index2}.coatings`}
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormControl>
                                              <MultiSelect
                                                className="min-h-[35px] max-h-[35px]"
                                                options={coatingList}
                                                onValueChange={field.onChange}
                                                defaultValue={field.value || []}
                                                placeholder="เลือกเคลือบ"
                                                variant="inverted"
                                                animation={2}
                                                maxCount={0}
                                                {...field}
                                              />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />
                                    </div>
                                    <div className="flex justify-end">
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="icon"
                                        onClick={() =>
                                          handleRemovePriceConfig(index, index2)
                                        }
                                      >
                                        <CircleMinus className="text-[#d32e2e]" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        ))}
                  </div>
                  <div className="flex items-center justify-start w-full px-[24px]">
                    <FormField
                      control={form.control}
                      name="printingConfigs"
                      render={({ field }) => (
                        <FormItem {...field}>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <DrawerFooter>
                  <div className="flex flex-row items-center justify-end gap-[16px] w-full h-full">
                    <DrawerClose asChild>
                      <Button variant="outline">ยกเลิก</Button>
                    </DrawerClose>
                    <Button type="submit">บันทึก</Button>
                  </div>
                </DrawerFooter>
              </form>
            </Form>
          </div>
        </DrawerContent>
      </Drawer>
      <PrintingModal
        openModal={openPrinting}
        setOpenModal={(value) => setOpenPrinting(value)}
        printing={printings}
        addPrinting={(value: string[]) => handlerAddPrintingConfig(value)}
      />
    </>
  );
};

export default ModelConfigDrawer;
