'use client';

import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import { isEmpty, isUndefined } from 'lodash';
import { Check } from 'lucide-react';

type Props = {
  setOpenModal: (e: boolean) => void;
  openModal: boolean;
  printing: any;
  addPrinting: (e: string[]) => void;
};

const PrintingModal = ({
  setOpenModal,
  openModal,
  printing,
  addPrinting,
}: Props) => {
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    setSelected([]);
  }, [openModal]);

  return (
    <Dialog
      onOpenChange={() => setOpenModal(false)}
      open={openModal}
      defaultOpen={openModal}
    >
      <DialogContent className="w-full max-w-[450px]">
        <DialogHeader>
          <DialogTitle>เพิ่มการพิมพ์</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-[8px] flex-nowrap py-[24px]">
          {!isUndefined(printing) &&
            !isEmpty(printing) &&
            printing.map((item: any, index: number) => {
              const isSelected = selected.includes(item.name);
              return (
                <div
                  key={index}
                  className={`flex flex-row items-center justify-between p-[8px] hover:cursor-pointer hover:bg-[#EEF3FF] rounded-[8px] ${isSelected ? 'bg-[#EEF3FF]' : ''}`}
                  onClick={() => {
                    if (isSelected) {
                      setSelected(
                        selected.filter((name) => name !== item.name),
                      );
                    } else {
                      setSelected([...selected, item.name]);
                    }
                  }}
                >
                  <div className="text-[#000] text-[14px]">{item.name}</div>

                  {isSelected && <Check className="w-4 h-4 text-primary" />}
                </div>
              );
            })}
        </div>
        <DialogFooter>
          <div className="flex flex-row items-center gap-[16px] w-full h-full">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpenModal(false)}
              className="w-full"
            >
              ยกเลิก
            </Button>
            <Button
              type="button"
              className="w-full"
              disabled={selected.length === 0}
              onClick={() => {
                setOpenModal(false);
                addPrinting(selected);
              }}
            >
              เพิ่ม
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PrintingModal;
