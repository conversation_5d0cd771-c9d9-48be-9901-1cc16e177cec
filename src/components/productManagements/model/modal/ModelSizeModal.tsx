'use client';

import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { ActionType } from '../../../../enum/action';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@/components/ui/label';
import { ApiResponse } from '@/utils/types';
import { isEmpty, isUndefined } from 'lodash';
import { Toast } from '@/utils/toast';
import unfoldedSizeService from '@/services/unfoldedSize';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import Swal from 'sweetalert2';
import modelSizeService, { ModelSizeRequest } from '@/services/modelSize';
import { inputNumberFormat, onKeyDown } from '@/utils/input-number';

type Props = {
  setOpenModal: (e: boolean) => void;
  openModal: boolean;
  action: string;
  modelId: number;
  fetchData: (id: number) => void;
  editId?: number;
};

const FormSchema = z
  .object({
    width: z.coerce
      .number({
        required_error: 'กรุณากรอกความกว้าง',
        invalid_type_error: 'กรุณากรอกความกว้าง',
      })
      .gte(0.01, 'ความกว้างเริ่มต้น 0.01 cm.'),
    length: z.coerce
      .number({
        invalid_type_error: 'กรุณากรอกความหนา',
      })
      .gte(0.01, 'ความหนาเริ่มต้น 0.01 cm.')
      .optional()
      .nullable(),
    height: z.coerce
      .number({
        required_error: 'กรุณากรอกความสูง',
        invalid_type_error: 'กรุณากรอกความสูง',
      })
      .gte(0.01, 'ความสูงเริ่มต้น 0.01 cm.'),
    unfoldedWidth: z.coerce
      .number({
        required_error: 'กรุณากรอกความกว้าง',
        invalid_type_error: 'กรุณากรอกความกว้าง',
      })
      .gte(0.01, 'ความกว้างเริ่มต้น 0.01 cm.'),
    unfoldedHeight: z.coerce
      .number({
        required_error: 'กรุณากรอกความสูง',
        invalid_type_error: 'กรุณากรอกความสูง',
      })
      .gte(0.01, 'ความสูงเริ่มต้น 0.01 cm.'),
    unfoldedSizeId: z
      .string({
        required_error: 'กรุณาเลือกขนาดใบพิมพ์',
      })
      .min(1, 'กรุณาเลือกขนาดใบพิมพ์')
      .refine(
        (value) => value !== 'เลือกสินค้า' && value !== 'เลือกขนาดใบพิมพ์',
        {
          message: 'กรุณาเลือกขนาดใบพิมพ์',
        },
      ),
    status3d: z.boolean({
      required_error: 'ระบุรูปทรง',
      invalid_type_error: 'ระบุรูปทรง',
    }),
  })
  .superRefine((data, ctx) => {
    // Only validate length when status3d is true and length is provided
    if (
      data.status3d &&
      data.length !== null &&
      data.length !== undefined &&
      data.width &&
      data.length > data.width
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'ความหนาต้องไม่มากกว่าความกว้าง',
        path: ['length'],
      });
    }

    // Require length when status3d is true
    if (
      data.status3d &&
      (data.length === null || data.length === undefined || data.length <= 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'กรุณากรอกความหนา',
        path: ['length'],
      });
    }
  });

const ModelSizeModal = ({
  setOpenModal,
  openModal,
  action,
  modelId,
  fetchData,
  editId,
}: Props) => {
  const [unfoldedSizes, setUnfoldedSizes] = useState<any>([]);
  const [modelSize, setModelSize] = useState<any>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      width: undefined,
      length: undefined,
      height: undefined,
      unfoldedWidth: undefined,
      unfoldedHeight: undefined,
      unfoldedSizeId: '',
      status3d: true,
    },
  });

  const getSelectUnfoldedSize = async () => {
    const res: ApiResponse = await unfoldedSizeService.getSelectUnfoldedSize();
    if (res.status) {
      setUnfoldedSizes(res.data.content);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  const getModelSizeById = async (id: number) => {
    const res: ApiResponse = await modelSizeService.getModelSizeById(id);
    if (res.status) {
      setModelSize(res.data);

      form.setValue('width', res.data.width / 10);
      form.setValue('length', res.data.length / 10);
      form.setValue('height', res.data.height / 10);
      form.setValue('unfoldedWidth', res.data.unfoldedWidth / 10);
      form.setValue('unfoldedHeight', res.data.unfoldedHeight / 10);
      form.setValue('unfoldedSizeId', res.data.unfoldedSizeName);
      form.setValue('status3d', res.data.isThreeD);

      await form.trigger(['unfoldedSizeId']);
    } else {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
      });
    }
  };

  useEffect(() => {
    if (openModal) {
      getSelectUnfoldedSize();
      form.reset({
        width: undefined,
        length: undefined,
        height: undefined,
        unfoldedWidth: undefined,
        unfoldedHeight: undefined,
        unfoldedSizeId: '',
        status3d: true,
      });

      if (action === ActionType.EDIT && !isUndefined(editId)) {
        getModelSizeById(editId);
      }
    }
  }, [openModal, action, editId]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      const { unfoldedWidth, unfoldedHeight, unfoldedSizeId } = value;

      if (
        !isUndefined(unfoldedWidth) &&
        unfoldedWidth > 0 &&
        !isUndefined(unfoldedHeight) &&
        unfoldedHeight > 0 &&
        unfoldedSizeId &&
        unfoldedSizeId !== 'เลือกสินค้า' &&
        unfoldedSizeId !== 'เลือกขนาดใบพิมพ์'
      ) {
        const unfoldedSize = unfoldedSizes.find(
          (item: any) => item.name === unfoldedSizeId,
        );

        if (!isUndefined(unfoldedSize)) {
          const { width, height } = unfoldedSize;

          const unfoldedWidthMM = Number(unfoldedWidth) * 10;
          const unfoldedHeightMM = Number(unfoldedHeight) * 10;

          let validate: boolean = false;

          if (width >= unfoldedWidthMM && height >= unfoldedHeightMM) {
            validate = true;
          }
          if (width >= unfoldedHeightMM && height >= unfoldedWidthMM) {
            validate = true;
          }

          if (!validate && !form.formState.errors.unfoldedSizeId) {
            form.setError('unfoldedSizeId', {
              type: 'validate',
              message: 'ไม่สามารถใช้ใบพิมพ์นี้ได้',
            });
          }

          if (
            validate &&
            form.formState.errors.unfoldedSizeId?.type === 'validate'
          ) {
            form.clearErrors('unfoldedSizeId');
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [unfoldedSizes, form]);

  const onSubmit = async (values: any) => {
    const unfoldedSize = unfoldedSizes.find(
      (item: any) => item.name === values.unfoldedSizeId,
    );
    if (isUndefined(unfoldedSize)) {
      Toast.fire({
        title: 'เกิดข้อผิดพลาด',
        text: 'ไม่พบข้อมูลใบพิมพ์',
        icon: 'error',
      });
    }

    const req: ModelSizeRequest = {
      modelId: modelId,
      unfoldedSizeId: unfoldedSize.id,
      unfoldedWidth: Number(values.unfoldedWidth) * 10,
      unfoldedHeight: Number(values.unfoldedHeight) * 10,
      width: Number(values.width) * 10,
      height: Number(values.height) * 10,
      length: Number(values.length) * 10,
      isThreeD: values.status3d,
    };

    const res: ApiResponse =
      action !== ActionType.EDIT
        ? await modelSizeService.createModelSize(req)
        : await modelSizeService.updateModelSize(modelSize.id, req);

    if (!res.status) {
      setOpenModal(false);
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        text: res.message,
        icon: 'error',
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
      }).then();
    } else {
      fetchData(modelId);
      setOpenModal(false);
      Swal.fire({
        title: 'สำเร็จ',
        text: res.message,
        icon: 'success',
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: true,
        confirmButtonColor: '#000000',
        confirmButtonText: 'OK',
        reverseButtons: true,
      }).then();
    }
  };

  return (
    <Dialog
      onOpenChange={() => setOpenModal(false)}
      open={openModal}
      defaultOpen={openModal}
    >
      <DialogContent className="w-full max-w-[900px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>
                {action === ActionType.EDIT
                  ? 'แก้ไขขนาดโมเดล'
                  : 'เพิ่มขนาดโมเดล'}
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col gap-[24px] flex-nowrap py-[24px]">
              <Label>ขนาดสินค้า (cm)</Label>
              <div className="flex flex-row gap-[18px] w-full bg-[#FAFAFA] rounded-[16px] p-[16px]">
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <Label>Width</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none bg-white"
                            placeholder="0"
                            {...field}
                            value={field.value ?? ''}
                            onChange={(event) =>
                              inputNumberFormat(event, form, `width`, 2)
                            }
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {form.watch('status3d') && (
                  <div className="grow">
                    <FormField
                      control={form.control}
                      name="length"
                      render={({ field }) => (
                        <FormItem>
                          <Label>Length</Label>
                          <FormControl>
                            <Input
                              type="text"
                              className="resize-none bg-white"
                              placeholder="0"
                              {...field}
                              value={field.value ?? ''}
                              onChange={(event) =>
                                inputNumberFormat(event, form, `length`, 2)
                              }
                              onKeyDown={onKeyDown}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
                <div className="grow">
                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <Label>Height</Label>
                        <FormControl>
                          <Input
                            type="text"
                            className="resize-none bg-white"
                            placeholder="0"
                            {...field}
                            value={field.value ?? ''}
                            onChange={(event) =>
                              inputNumberFormat(event, form, `height`, 2)
                            }
                            onKeyDown={onKeyDown}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <Label>ขนาดกางออก (cm)</Label>
              <div className="flex flex-row items-start  gap-[24px] w-full">
                <div className="flex-1/2 flex flex-row items-start">
                  <div className="flex-1/2">
                    <FormField
                      control={form.control}
                      name="unfoldedWidth"
                      render={({ field }) => (
                        <FormItem>
                          <Label>Width</Label>
                          <FormControl>
                            <Input
                              type="text"
                              className="resize-none"
                              placeholder="0"
                              {...field}
                              value={field.value ?? ''}
                              onChange={(event) =>
                                inputNumberFormat(
                                  event,
                                  form,
                                  `unfoldedWidth`,
                                  2,
                                )
                              }
                              onKeyDown={onKeyDown}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grow flex h-full items-center">
                    <div className="px-[8px] pt-[30px]">x</div>
                  </div>
                  <div className="flex-1/2">
                    <FormField
                      control={form.control}
                      name="unfoldedHeight"
                      render={({ field }) => (
                        <FormItem>
                          <Label>Height</Label>
                          <FormControl>
                            <Input
                              type="text"
                              className="resize-none"
                              placeholder="0"
                              {...field}
                              value={field.value ?? ''}
                              onChange={(event) =>
                                inputNumberFormat(
                                  event,
                                  form,
                                  `unfoldedHeight`,
                                  2,
                                )
                              }
                              onKeyDown={onKeyDown}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                <div className="flex-1/2">
                  <FormField
                    control={form.control}
                    name={'unfoldedSizeId'}
                    render={({ field }) => (
                      <FormItem>
                        <Label>ขนาดใบพิมพ์</Label>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="mb-0">
                              <SelectValue placeholder="เลือกขนาดใบพิมพ์" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="เลือกสินค้า">
                              เลือกขนาดใบพิมพ์
                            </SelectItem>
                            {!isEmpty(unfoldedSizes) &&
                              unfoldedSizes.map((item: any, index: number) => (
                                <SelectItem key={index} value={item.name}>
                                  {item.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="pt-2" />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className="flex flex-row gap-[24px] w-full items-center justify-start">
                <div className="flex items-center gap-3">
                  <FormField
                    control={form.control}
                    name="status3d"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center gap-2">
                        <FormControl>
                          <Checkbox
                            className="mb-0"
                            checked={field.value ?? true}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="text-sm font-normal">
                          3D Model
                        </FormLabel>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <div className="flex flex-row items-center gap-[16px] w-full h-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpenModal(false)}
                  className="w-full"
                >
                  ยกเลิก
                </Button>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={form.formState.isSubmitting}
                >
                  {action === ActionType.EDIT ? 'แก้ไข' : 'เพิ่ม'}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ModelSizeModal;
