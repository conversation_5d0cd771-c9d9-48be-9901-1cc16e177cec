import api from '@/api/Http';

const PATH = '/shop-info';

type FetchData = {
  page: number;
  size: number;
  ascending: boolean;
  search?: string;
  isActive?: boolean;
};

export type ShopInfoRequest = {
  name: string;
  iconUrl: string;
  taxId: number;
  address?: string;
  zipCode: number;
  province: string;
  district: string;
  subDistrict: string;
  phoneNumber: string;
  email?: string;
  lineId?: string;
  imageLineUrl?: string;
  website?: string;
  linkGoogleMap?: string;
  isActive?: boolean;
};

const shopInfoService = {
  createShopInfo: async (req: ShopInfoRequest) => {
    try {
      const url = `${PATH}`;
      const res = await api.post(url, req);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
  getShopInfo: async ({
    page,
    size,
    ascending,
    search,
    isActive,
  }: FetchData) => {
    try {
      let url = `${PATH}/page?page=${page}&size=${size}&ascending=${ascending}`;

      if (search) url += `&search=${search}`;
      if (isActive) url += `&isActive=${isActive}`;

      const res = await api.get(url);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
  getShopInfoById: async (id: number) => {
    try {
      const url = `${PATH}/${id}`;
      const res = await api.get(url);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
  updateShopInfo: async (id: number, req: ShopInfoRequest) => {
    try {
      const url = `${PATH}/${id}`;
      const res = await api.put(url, req);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
  deleteShopInfo: async (id: number) => {
    try {
      const url = `${PATH}/${id}`;
      const res = await api.delete(url);
      return res.data;
    } catch (err: any) {
      return err?.data;
    }
  },
};

export default shopInfoService;
