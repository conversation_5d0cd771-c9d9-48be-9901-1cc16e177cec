const config = {
  plugins: ['@tailwindcss/postcss'],
};

export default config;
// export default {
//   plugins: {
//     '@tailwindcss/postcss': {},
//     autoprefixer: {},
//   },
// };

// /** @type {import('postcss-load-config').Config} */
// const config = {
//   reactStrictMode: false,
//   compiler: {
//     styledComponents: true,
//   },
//   plugins: {
//     tailwindcss: {},
//   },
// };
//
// export default config;
