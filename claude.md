# Digiboxs Admin Panel

## Project Description

**Digiboxs Admin** is a comprehensive administrative dashboard built with Next.js for managing a digital printing and packaging business. The application serves as the backend management system for Digiboxs, a company that specializes in custom printing services, packaging solutions, and related products.

### Purpose and Functionality

The admin panel provides complete management capabilities for:
- **Product Management**: Managing printing products, materials, models, and configurations
- **Order Management**: Handling customer orders, tracking order status, and payment processing
- **Content Management**: Managing blog articles, categories, and content publishing
- **Discount Management**: Creating and managing promotional campaigns and discount categories
- **User Authentication**: Secure admin access with token-based authentication
- **File Management**: Image and document upload capabilities
- **Financial Management**: Accounting and financial tracking features

## Project Structure

```
digibox-admin/
├── src/
│   ├── api/                    # HTTP client configuration
│   │   └── Http.ts            # Axios instance with interceptors
│   ├── app/                   # Next.js App Router structure
│   │   ├── (private)/         # Protected routes
│   │   │   ├── (article)/     # Blog management routes
│   │   │   ├── (discount)/    # Discount management routes
│   │   │   └── (product-management)/ # Product management routes
│   │   ├── login/             # Authentication pages
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── blog/              # Blog management components
│   │   ├── discount/          # Discount management components
│   │   ├── layout/            # Layout components (sidebar, header)
│   │   ├── productManagements/ # Product management components
│   │   ├── shared/            # Reusable components
│   │   └── ui/                # UI component library (shadcn/ui)
│   ├── enum/                  # TypeScript enums
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   ├── services/              # API service layer
│   │   ├── auth.ts            # Authentication services
│   │   ├── product.ts         # Product management services
│   │   ├── blog.ts            # Blog management services
│   │   ├── discount.ts        # Discount management services
│   │   └── fileUploader.ts    # File upload services
│   ├── store/                 # State management (Zustand)
│   ├── styles/                # Styling files
│   ├── type/                  # TypeScript type definitions
│   ├── utils/                 # Utility functions
│   └── middleware.ts          # Next.js middleware for authentication
├── public/                    # Static assets
│   ├── fonts/                 # Custom fonts (LINE Seed Sans TH)
│   └── icons/                 # Application icons
├── Dockerfile                 # Production Docker configuration
├── dev.Dockerfile            # Development Docker configuration
├── staging.Dockerfile        # Staging Docker configuration
├── package.json              # Dependencies and scripts
├── next.config.ts            # Next.js configuration
├── tsconfig.json             # TypeScript configuration
├── components.json           # shadcn/ui configuration
└── postcss.config.mjs        # PostCSS configuration
```

## Tech Stack

### Core Technologies
- **Framework**: Next.js 15.3.5 (React 19.1.0)
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4 with PostCSS
- **State Management**: Zustand 4.4.7
- **HTTP Client**: Axios for API communication
- **Authentication**: Cookie-based JWT tokens

### UI Components & Libraries
- **Component Library**: shadcn/ui with Radix UI primitives
- **Icons**: Lucide React, Tabler Icons, MUI Icons
- **Forms**: React Hook Form with Zod validation
- **Rich Text Editor**: React Quill, Jodit React
- **Data Tables**: TanStack React Table
- **Charts**: Recharts
- **Notifications**: SweetAlert2, Sonner
- **Date Handling**: Day.js, date-fns

### Development Tools
- **Linting**: ESLint with Airbnb TypeScript config
- **Code Formatting**: Prettier
- **Build Tool**: SWC for fast compilation
- **Package Manager**: Yarn
- **Environment Management**: env-cmd for multiple environments

### Additional Features
- **Image Processing**: React Image File Resizer, Sharp
- **Styled Components**: For custom styling alongside Tailwind
- **Infinite Scroll**: React Infinite Scroll Component
- **Media Player**: React Player
- **Copy to Clipboard**: Copy-to-clipboard utility
- **Theme Management**: next-themes for dark/light mode

## Key Features

### 1. Product Management System
- **Products**: Complete CRUD operations for printing products
- **Models**: 3D model management for packaging designs
- **Materials**: Material catalog with specifications and pricing
- **Categories & Tags**: Hierarchical product organization
- **Printing Systems**: Configuration of printing methods and techniques
- **Special Techniques**: Advanced printing and finishing options
- **Size Configurations**: Unfolded sizes, area percentages, model sizes
- **Coating Options**: Surface treatment and finishing options

### 2. Order Management
- **Order Tracking**: Complete order lifecycle management
- **Status Management**: Multi-stage order processing workflow
  - รอเสนอราคา (Awaiting Quote)
  - รอชำระเงิน (Awaiting Payment)
  - ตรวจสอบการชำระเงิน (Payment Verification)
  - ดำเนินการผลิต (In Production)
  - สำเร็จ (Completed)
  - ยกเลิก (Cancelled)
- **Payment Processing**: Payment verification and tracking
- **Claims Management**: Order issue resolution system

### 3. Content Management System
- **Blog Management**: Full-featured blog with rich text editing
- **Article Categories**: Hierarchical content organization
- **Article Types**: Content classification system
- **SEO Features**: URL slugs, meta descriptions, keywords
- **Publishing Workflow**: Draft and published states
- **Media Gallery**: Image management for blog content

### 4. Discount & Promotion System
- **Discount Categories**: Organized promotional campaigns
- **Time-based Promotions**: Start and end date management
- **Image Support**: Visual promotional materials
- **Status Management**: Active/inactive discount control

### 5. Authentication & Security
- **JWT Authentication**: Secure token-based authentication
- **Route Protection**: Middleware-based access control
- **Role-based Access**: Admin-specific functionality
- **Session Management**: Automatic token refresh and logout

## API Integration

### Backend Services
- **Production API**: `https://api.digiboxs.com/api`
- **Development API**: `https://api-dev.digiboxs.com/api`
- **Blog API**: `https://bo-api.lucablock.io/api` (separate service)

### Service Architecture
The application follows a service-oriented architecture with dedicated service files for each domain:
- Authentication services (`/user` endpoints)
- Product management services (`/product`, `/material`, `/model` endpoints)
- Blog services (`/blog` endpoints)
- File upload services (`/file-uploader` endpoints)
- Discount services (`/discount` endpoints)

### HTTP Client Configuration
- Automatic JWT token injection via Axios interceptors
- Request/response error handling
- 30-second timeout configuration
- CORS headers for cross-origin requests

## Environment Configuration

The application supports multiple deployment environments:
- **Local Development**: `.env.local`
- **Development**: `.env.dev`
- **Staging**: `.env.staging`
- **Production**: `.env`

Each environment has its own API endpoints and configuration settings.

## Docker Support

Multiple Docker configurations are provided:
- **Development**: `dev.Dockerfile` for development environment
- **Staging**: `staging.Dockerfile` for staging deployment
- **Production**: `Dockerfile` for production deployment

## Security and Operational Restrictions

### Critical Security Guidelines

⚠️ **IMPORTANT: The following restrictions must be strictly followed:**

#### 1. Library Management
- **DO NOT modify any library files** in the `node_modules` directory
- **DO NOT update or upgrade any library versions** without explicit approval
- **DO NOT install new dependencies** without proper security review
- Use only the package manager commands for dependency management

#### 2. Environment Security
- **DO NOT read any `.env.*` files** or access environment variables directly
- **DO NOT expose sensitive configuration** in client-side code
- **DO NOT commit environment files** to version control
- All environment variables should be managed through proper deployment pipelines

#### 3. File System Access
- **DO NOT modify configuration files** outside of the `src` directory without approval
- **DO NOT access system files** or directories outside the project scope
- **DO NOT modify Docker configurations** without proper testing

#### 4. API Security
- **DO NOT expose API keys** or authentication tokens in client code
- **DO NOT bypass authentication middleware** or security checks
- **DO NOT modify HTTP interceptors** without security review

#### 5. Code Integrity
- **DO NOT disable TypeScript strict mode** or linting rules
- **DO NOT remove security-related middleware** or authentication checks
- **DO NOT modify the authentication flow** without proper testing

### Operational Guidelines
- All changes should go through proper code review
- Security-sensitive modifications require additional approval
- Environment-specific configurations should be managed through deployment pipelines
- Regular security audits should be conducted on dependencies

---

*This documentation provides a comprehensive overview of the Digiboxs Admin Panel. For specific implementation details, refer to the individual component and service files within the codebase.*
