{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/api/*": ["src/api/*"], "@/app/*": ["src/app/*"], "@/components/*": ["src/components/*"], "@/hooks/*": ["src/hooks/*"], "@/lib/*": ["src/lib/*"], "@/libs/*": ["src/libs/*"], "@/layouts/*": ["src/layouts/*"], "@/services/*": ["src/services/*"], "@/store/*": ["src/store/*"], "@/styles/*": ["src/styles/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules"]}